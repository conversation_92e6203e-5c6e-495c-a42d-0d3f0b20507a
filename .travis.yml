os: linux
dist: xenial
language: python
python:
  - "3.5"
  - "3.6"
  - "3.7"
# Command to install dependencies
install:
  - pip install -r requirements/ci.pip
  - pip install coveralls

# Command to run tests
script:
  - coverage run -m unittest discover --start-directory test/
  - set -e
  - cd docs
  - make html
  - cd ..
  - doctr deploy --built-docs docs/_build/html .
after_success:
  coveralls
env:
  global:
    - secure: "sY6rUVIOxn4Mt4YieIvUDZToKPMjH4yk6YdVZLHfhmVlZW/st4AKkks+3jTZcNXGhPa8pEopS21FIadXoQ64NDD63F3ZuZNhyHM2Bq2ZfyPB4DcyYa7lasmlJIYWdAHz1bHp3z9YPvyoa4ieTkh+ZJCJ0kWUZF455giuK631OdZ3NBl3ot4Ef4U3rmTXfShRjbDG6u7gwu/n6D8FmAGg0jMgwAGYfrQ3EtPWPzlmdn39nidoYWbh/HFhKBZ+1QFYhj6Yfh4JZkktl0hXCyyDFrPj7Sn+p/+C9+PC+2t+WjkEov1CQd9ZtpjMxH1Y+Cy3KXJBjQknn7Mz5TqX4Rsbp+iU7kJEM7sikL0IriB4dzsShHgf3izRz4V/tivUg5Fv9E0SFIe8TUnqzUeHjv39jCFJf7lPQCtZWiK+KeBo80s/McqtaPxhcM5IgiPyHFExy+GbUzD06H/VlIttIlfzxTQpp5UOqkoDERP9g5PS8aw4R0f8P1OoWQ7uuVjZu/PXK3chqolAUbp1ZVn041LaKuW/ykZ1aCEjyslrD1lZVZ+aizLlVEOtSj9pRcXOKAjfqiM+oMbVymOsOkD1Tb5srx7EWA9MAbTNw8ep0Iid2mr7b8zCnqlFcK8KP8CrNKGu06oeN7JCGfGDArWG+Mkp4o0z2H70USjgWiccr3p38VA="

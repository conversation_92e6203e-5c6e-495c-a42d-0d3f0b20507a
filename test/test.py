from datetime import datetime, timezone
from klvdata.misb0601 import PrecisionTimeStamp
from klvdata.common import datetime_to_bytes, bytes_to_datetime

# Get current time
current_time = datetime.now(timezone.utc)
print(f"Current time: {current_time}")

# Convert to bytes for transmission
current_time_bytes = datetime_to_bytes(current_time)
print(f"Current time as bytes: {current_time_bytes.hex()}")

# Create PrecisionTimeStamp and get complete packet
current_timestamp_obj = PrecisionTimeStamp(current_time_bytes)
current_complete_packet = bytes(current_timestamp_obj)
print(f"Current time TLV packet: {current_complete_packet.hex()}")

reverse_current_time_value = current_complete_packet[2:]

print(f"Reverse current time value: {reverse_current_time_value.hex()}")

reverse_current_time = bytes_to_datetime(reverse_current_time_value)

print(f"Reverse current time: {reverse_current_time}")
    